import asyncio
import tempfile
import os
from typing import List, Optional
from pydub import AudioSegment
from pydub.silence import split_on_silence

from acva_ai.llm.azure_audio_call import transcribe_audio_file
from acva_ai.utils.usage import ResponseUsage
from acva_ai._params import OPENAI_TRANSCRIPTION_MODEL_ID


async def _get_file_size_mb(file_path: str) -> float:
    """Get file size in megabytes."""
    return os.path.getsize(file_path) / (1024 * 1024)


async def _get_audio_size_mb(audio_segment: AudioSegment) -> float:
    """Estimate audio segment size in megabytes."""
    return len(audio_segment.raw_data) / (1024 * 1024)


async def _convert_to_wav(input_path: str) -> str:
    """
    Convert any audio file to WAV format.

    Args:
        input_path: Path to the input audio file

    Returns:
        Path to the converted WAV file
    """
    # Create a temporary file for the WAV output
    temp_dir = tempfile.mkdtemp()
    output_path = os.path.join(temp_dir, "converted_audio.wav")

    # Load and convert the audio file - use asyncio.to_thread for file operations
    audio = await asyncio.to_thread(AudioSegment.from_file, input_path)
    await asyncio.to_thread(audio.export, output_path, format="wav")

    print(f"Converted audio file to WAV: {output_path}")
    return output_path


async def _split_large_chunk(
    chunk: AudioSegment,
    max_size_mb: float,
    min_silence_len: int = 500,
    silence_thresh_offset: int = -16,
    keep_silence: int = 250,
) -> List[AudioSegment]:
    """
    Recursively split a chunk that's too large.
    Uses more aggressive splitting parameters for oversized chunks.
    """
    chunk_size = await _get_audio_size_mb(chunk)
    if chunk_size <= max_size_mb:
        return [chunk]

    # Calculate silence threshold
    silence_thresh = chunk.dBFS + silence_thresh_offset

    # Try splitting with more aggressive parameters - use to_thread for CPU-intensive operation
    sub_chunks = await asyncio.to_thread(
        split_on_silence,
        chunk,
        min_silence_len=min_silence_len,
        silence_thresh=silence_thresh,
        keep_silence=keep_silence,
    )

    # If no splits were made, force split by time
    if len(sub_chunks) <= 1:
        # Calculate chunk duration to stay under size limit
        target_duration = (
            len(chunk) * (max_size_mb / chunk_size) * 0.9
        )  # 90% safety margin
        target_duration = max(30000, int(target_duration))  # At least 30 seconds

        sub_chunks = []
        for i in range(0, len(chunk), int(target_duration)):
            sub_chunks.append(chunk[i : i + int(target_duration)])

    # Recursively check each sub-chunk
    final_chunks = []
    for sub_chunk in sub_chunks:
        sub_chunk_size = await _get_audio_size_mb(sub_chunk)
        if sub_chunk_size > max_size_mb:
            # Recursively split if still too large
            split_results = await _split_large_chunk(
                sub_chunk,
                max_size_mb,
                min_silence_len=max(250, min_silence_len // 2),  # More aggressive
                silence_thresh_offset=silence_thresh_offset - 2,
                keep_silence=max(100, keep_silence // 2),
            )
            final_chunks.extend(split_results)
        else:
            final_chunks.append(sub_chunk)

    return final_chunks


async def transcribe_audio_with_splitting(
    audio_file_path: str,
    model_id: str = OPENAI_TRANSCRIPTION_MODEL_ID,
    language: Optional[str] = None,
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
    min_silence_len: int = 1000,
    silence_thresh_offset: int = -14,
    keep_silence: int = 500,
    max_chunk_size_mb: float = 24.0,
    file_size_threshold_mb: float = 25.0,
    batch_size: int = 5,
) -> str:
    """
    Transcribe an audio file by splitting on silence and processing chunks in parallel batches.
    Only splits if the file is larger than the threshold.
    Automatically converts input to WAV format.

    Args:
        audio_file_path: Path to the audio file (any supported format)
        model_id: Azure deployment name for Whisper
        language: Optional language code (e.g., 'ro', 'en')
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs
        min_silence_len: Minimum silence length in milliseconds
        silence_thresh_offset: Silence threshold offset from average dBFS
        keep_silence: Amount of silence to keep at split points in milliseconds
        max_chunk_size_mb: Maximum chunk size in MB
        file_size_threshold_mb: File size threshold to trigger splitting
        batch_size: Number of chunks to process in parallel per batch

    Returns:
        Complete transcription text
    """
    if not os.path.exists(audio_file_path):
        raise FileNotFoundError(f"Audio file not found: {audio_file_path}")

    # Convert audio to WAV format
    wav_file_path = await _convert_to_wav(audio_file_path)

    try:
        # Check file size first
        file_size_mb = await _get_file_size_mb(wav_file_path)
        print(f"Audio file size: {file_size_mb:.2f} MB")

        # If file is small enough, transcribe directly without splitting
        if file_size_mb <= file_size_threshold_mb:
            print("File is small enough, transcribing without splitting...")
            return await transcribe_audio_file(
                audio_file_path=wav_file_path,
                model_id=model_id,
                language=language,
                use_cache=use_cache,
                response_usage=response_usage,
            )

        # File is large, proceed with splitting
        print(
            f"File is larger than {file_size_threshold_mb} MB, proceeding with splitting..."
        )

        # Load audio file
        print(f"Loading audio file: {wav_file_path}")
        audio = await asyncio.to_thread(AudioSegment.from_file, wav_file_path)

        # Calculate silence threshold
        silence_thresh = audio.dBFS + silence_thresh_offset

        # Split on silence
        print("Splitting audio on silence...")
        chunks = await asyncio.to_thread(
            split_on_silence,
            audio,
            min_silence_len=min_silence_len,
            silence_thresh=silence_thresh,
            keep_silence=keep_silence,
        )

        print(f"Initial split created {len(chunks)} chunks")

        # Recombine short chunks and ensure all chunks are under size limit
        final_chunks = []
        temp_chunk = AudioSegment.empty()

        for chunk in chunks:
            # Check if this chunk alone is too large
            chunk_size = await _get_audio_size_mb(chunk)
            if chunk_size > max_chunk_size_mb:
                # Add any accumulated temp_chunk first
                if len(temp_chunk) > 0:
                    final_chunks.append(temp_chunk)
                    temp_chunk = AudioSegment.empty()

                # Recursively split the large chunk
                print(f"Chunk is {chunk_size:.2f} MB, recursively splitting...")
                split_chunks = await _split_large_chunk(chunk, max_chunk_size_mb)
                final_chunks.extend(split_chunks)
            else:
                # Try to combine with temp_chunk
                combined_chunk = temp_chunk + chunk
                combined_size = await _get_audio_size_mb(combined_chunk)
                if combined_size <= max_chunk_size_mb:
                    temp_chunk = combined_chunk
                else:
                    # temp_chunk is full, add it to final_chunks
                    if len(temp_chunk) > 0:
                        final_chunks.append(temp_chunk)
                    temp_chunk = chunk

        # Add the last temp_chunk
        if len(temp_chunk) > 0:
            final_chunks.append(temp_chunk)

        print(f"After processing: {len(final_chunks)} final chunks")

        # Verify all chunks are under size limit
        for i, chunk in enumerate(final_chunks):
            chunk_size = await _get_audio_size_mb(chunk)
            print(f"Chunk {i+1}: {chunk_size:.2f} MB")
            if chunk_size > max_chunk_size_mb:
                print(
                    f"Warning: Chunk {i+1} is still {chunk_size:.2f} MB > {max_chunk_size_mb} MB"
                )

        # Create temporary files for chunks
        temp_files = []
        temp_dir = tempfile.mkdtemp()

        try:
            # Export chunks to temporary files
            for i, chunk in enumerate(final_chunks):
                temp_file_path = os.path.join(temp_dir, f"chunk_{i+1}.wav")
                await asyncio.to_thread(chunk.export, temp_file_path, format="wav")
                temp_files.append(temp_file_path)

            # Process chunks in batches
            transcriptions = []

            for i in range(0, len(temp_files), batch_size):
                batch_files = temp_files[i : i + batch_size]
                print(
                    f"Processing batch {i//batch_size + 1} with {len(batch_files)} chunks..."
                )

                # Create tasks for parallel processing within the batch
                tasks = []
                for file_path in batch_files:
                    task = transcribe_audio_file(
                        audio_file_path=file_path,
                        model_id=model_id,
                        language=language,
                        use_cache=use_cache,
                        response_usage=response_usage,
                    )
                    tasks.append(task)

                # Execute batch in parallel
                batch_results = await asyncio.gather(*tasks)
                transcriptions.extend(batch_results)

            # Combine all transcriptions
            full_transcription = " ".join(transcriptions)

            print(
                f"Transcription completed. Total length: {len(full_transcription)} characters"
            )
            return full_transcription

        finally:
            # Cleanup temporary files
            for temp_file in temp_files:
                try:
                    await asyncio.to_thread(os.remove, temp_file)
                except Exception as e:
                    print(f"Warning: Could not remove temporary file {temp_file}: {e}")

            try:
                await asyncio.to_thread(os.rmdir, temp_dir)
            except Exception as e:
                print(f"Warning: Could not remove temporary directory {temp_dir}: {e}")
    except Exception as e:
        print(f"Error during transcription: {e}")
        raise


async def transcribe_audio_simple(
    audio_file_path: str,
    model_id: str = OPENAI_TRANSCRIPTION_MODEL_ID,
    language: Optional[str] = None,
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
) -> str:
    """
    Simple transcription without splitting (for smaller files).

    Args:
        audio_file_path: Path to the audio file
        model_id: Azure deployment name for Whisper
        language: Optional language code
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs

    Returns:
        Transcription text
    """
    return await transcribe_audio_file(
        audio_file_path=audio_file_path,
        model_id=model_id,
        language=language,
        use_cache=use_cache,
        response_usage=response_usage,
    )


async def test():
    """Test function for the transcription service."""
    # Example usage
    response_usage = ResponseUsage()

    # Test with a sample audio file
    result = await transcribe_audio_with_splitting(
        audio_file_path="/home/<USER>/Downloads/WhatsApp Audio 2025-02-19 at 16.01.12.mpeg",
        language="ro",  # Romanian
        response_usage=response_usage,
        batch_size=3,
    )

    print("Transcription result:")
    print(result)
    print(f"Usage: {response_usage}")

    print("Test function ready. Uncomment and provide audio file path to test.")


if __name__ == "__main__":
    asyncio.run(test())
