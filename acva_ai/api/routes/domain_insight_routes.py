import logging
from typing import List, Optional

import httpx
from fastapi import APIRouter, Body, Depends, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from acva_ai._params import ACVA_REPORT_FIELDS, API_KEY
from acva_ai.database import mongo_instance
from acva_ai.models.domain_insight import DomainInsight
from acva_ai.pipeline.domain_insights import process_domain_insights
from acva_ai.utils import verify_api_key
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)

domain_insight_router = APIRouter()


@domain_insight_router.post("/process-domain-insights", tags=["Methods"])
async def process_insights(
    transcript: str = Body(..., media_type="text/plain"),
    task_id: Optional[str] = None,
    api_key: str = Depends(verify_api_key),
):
    """
    Processes domain-specific insights from a transcript.

    This endpoint handles the optional fifth step of the medical transcription pipeline:
    analyzing the transcript for domain-specific insights based on provided domain knowledge.

    - **transcript**: Transcript text to analyze
    - **domain_insights**: List of domain insights to process against the transcript
    - **task_id**: Optional task ID to associate with this processing

    Returns a JSON object containing the processed domain insights with their relevance scores.
    """
    try:
        domain_insights = []
        insights = {"domain_insights": []}
        response = httpx.get(
            url=f"https://api.acva.ai/task/{task_id}/report-fields",
            headers={"X-API-Key": "Gr0oXNfCYs6KpO8izlaf0csijTtqU4or"},
        )
        if response.status_code == 200:
            for key, value in response.json().get("prompts").items():
                insights["domain_insights"].append(
                    DomainInsight(
                        insight_name=key, insight_extraction_prompt=value
                    ).model_dump()
                )
                domain_insights.append(
                    DomainInsight(insight_name=key, insight_extraction_prompt=value)
                )

            process_insights = await process_domain_insights(
                transcript=transcript, domain_insights=domain_insights
            )
            domain_insights = []
            for domain in process_insights:
                domain_insights.append(domain.model_dump())
            return JSONResponse(
                status_code=200,
                content={
                    "domain_insights": domain_insights,
                    "prompt_insights": insights,
                },
            )

    except Exception as e:
        logger.error(f"Error processing domain insights: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error processing domain insights: {str(e)}"
        )
